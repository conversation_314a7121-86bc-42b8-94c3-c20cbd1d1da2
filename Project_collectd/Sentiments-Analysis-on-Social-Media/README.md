About the Project

This project explores sentiment analysis using various machine learning and deep learning techniques on social media data. The aim is to analyze and understand sentiments expressed in textual data from platforms like Instagram, Facebook, Twitter, and Reddit.

Methodologies Used:

Natural Language Processing (NLP):
Utilized NLP techniques for text preprocessing, including tokenization, lemmatization, and removal of stop words and special characters.
Employed NLP methodologies to transform raw textual data into structured input suitable for sentiment analysis.
Long Short-term Memory (LSTM):
Developed an LSTM model architecture to capture temporal dependencies and nuances in sentiment analysis tasks.
Trained the LSTM model using preprocessed text data to predict sentiment labels with high accuracy.
Support Vector Machine (SVM):
Implemented SVM as a traditional machine learning approach for sentiment analysis.
Leveraged SVM's ability to classify text data into multiple sentiment classes using appropriate feature extraction techniques.
Random Forest:
Utilized the ensemble learning method of Random Forest for sentiment analysis tasks.
Trained a Random Forest classifier to predict sentiment labels by aggregating predictions from multiple decision trees.
Dataset Used:

The dataset consists of textual data collected from various social media platforms, including Instagram, Facebook, Twitter, and Reddit.
It comprises a total of 747 records, with sentiments categorized into three classes: Positive, Negative, and Neutral.
Literature Paper Review:

Reviewed relevant literature to understand existing methodologies and techniques for sentiment analysis.
Integrated insights from literature reviews to inform the selection and implementation of machine learning and deep learning models.
Project Deliverables:

Code Files:
NLP code file for data preprocessing and feature extraction.
Separate code files for LSTM, SVM, and Random Forest models, including training and evaluation scripts.
Dataset:
Provided the dataset used for sentiment analysis, facilitating reproducibility and further research.
Literature Paper:
Included a literature paper review discussing relevant studies and methodologies in sentiment analysis.
Report:
Prepared a comprehensive report detailing project objectives, methodologies, experiments, results, and conclusions.
Presentation:
Created a concise presentation summarizing key aspects of the project, including problem statement, methodologies, experiments, results, and future scope.
GitHub Repository:

All project materials, including code files, dataset, literature paper, report, and presentation, are available in the GitHub repository for reference and collaboration.
