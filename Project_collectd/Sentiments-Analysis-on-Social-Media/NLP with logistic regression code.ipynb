{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["Importing Sentiment Analysis dataset"], "metadata": {"id": "qAebbjyWoVvg"}}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "IXFv7e_YnfJd", "outputId": "bfd20fed-ed4e-4f9f-8f55-871feb049dad"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["[nltk_data] Downloading package stopwords to /root/nltk_data...\n", "[nltk_data]   Unzipping corpora/stopwords.zip.\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import re\n", "import nltk\n", "nltk.download('stopwords')\n", "from nltk.corpus import stopwords\n", "from nltk.stem.porter import PorterStemmer\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import accuracy_score\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns"]}, {"cell_type": "code", "source": ["#printing the stopwords in english\n", "print(stopwords.words('english'))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "M_tJmBvcqEPI", "outputId": "8fe23017-e94a-4504-e391-2bdfbb58d451"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["['i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves', 'you', \"you're\", \"you've\", \"you'll\", \"you'd\", 'your', 'yours', 'yourself', 'yourselves', 'he', 'him', 'his', 'himself', 'she', \"she's\", 'her', 'hers', 'herself', 'it', \"it's\", 'its', 'itself', 'they', 'them', 'their', 'theirs', 'themselves', 'what', 'which', 'who', 'whom', 'this', 'that', \"that'll\", 'these', 'those', 'am', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'having', 'do', 'does', 'did', 'doing', 'a', 'an', 'the', 'and', 'but', 'if', 'or', 'because', 'as', 'until', 'while', 'of', 'at', 'by', 'for', 'with', 'about', 'against', 'between', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'to', 'from', 'up', 'down', 'in', 'out', 'on', 'off', 'over', 'under', 'again', 'further', 'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 's', 't', 'can', 'will', 'just', 'don', \"don't\", 'should', \"should've\", 'now', 'd', 'll', 'm', 'o', 're', 've', 'y', 'ain', 'aren', \"aren't\", 'couldn', \"couldn't\", 'didn', \"didn't\", 'doesn', \"doesn't\", 'hadn', \"hadn't\", 'hasn', \"hasn't\", 'haven', \"haven't\", 'isn', \"isn't\", 'ma', 'mightn', \"mightn't\", 'mustn', \"mustn't\", 'needn', \"needn't\", 'shan', \"shan't\", 'shouldn', \"shouldn't\", 'wasn', \"wasn't\", 'weren', \"weren't\", 'won', \"won't\", 'wouldn', \"wouldn't\"]\n"]}]}, {"cell_type": "markdown", "source": ["Data Processing\n"], "metadata": {"id": "C_mkQfsFqSDf"}}, {"cell_type": "code", "source": ["# Install the chardet module\n", "!pip install chardet\n", "\n", "# Import the chardet module\n", "import chardet\n", "\n", "# Detect the encoding of the CSV file\n", "with open('projectML.csv', 'rb') as f:\n", "    encoding = chardet.detect(f.read())['encoding']\n", "\n", "# Read the CSV file with the detected encoding\n", "df = pd.read_csv('projectML.csv', encoding=encoding)\n", "\n", "# Verify that the data is loaded correctly\n", "df.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 223}, "id": "pKSqzZjnqUO0", "outputId": "a6d6e23d-8003-4a3a-94f2-af865f986f0d"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: chardet in /usr/local/lib/python3.10/dist-packages (5.2.0)\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["   Sno.                                               Text  Sentiment  \\\n", "0     1   Enjoying a beautiful day at the park!        ...          0   \n", "1     2   Traffic was terrible this morning.           ...          1   \n", "2     3   Just finished an amazing workout! üí™       ...          0   \n", "3     4   Excited about the upcoming weekend getaway!  ...          0   \n", "4     5   Trying out a new recipe for dinner tonight.  ...          2   \n", "\n", "         Timestamp            User    Platform  \n", "0  1/15/2023 12:30   User123          Twitter   \n", "1   1/15/2023 8:45   CommuterX        Twitter   \n", "2  1/15/2023 15:45   <PERSON><PERSON><PERSON>   \n", "3  1/15/2023 18:20   AdventureX      Facebook   \n", "4  1/15/2023 19:55   ChefCook       Instagram   "], "text/html": ["\n", "  <div id=\"df-ec1ad710-797d-4787-9b32-bc3249f6849e\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sno.</th>\n", "      <th>Text</th>\n", "      <th>Sentiment</th>\n", "      <th>Timestamp</th>\n", "      <th>User</th>\n", "      <th>Platform</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Enjoying a beautiful day at the park!        ...</td>\n", "      <td>0</td>\n", "      <td>1/15/2023 12:30</td>\n", "      <td>User123</td>\n", "      <td>Twitter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>Traffic was terrible this morning.           ...</td>\n", "      <td>1</td>\n", "      <td>1/15/2023 8:45</td>\n", "      <td>CommuterX</td>\n", "      <td>Twitter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>Just finished an amazing workout! üí™       ...</td>\n", "      <td>0</td>\n", "      <td>1/15/2023 15:45</td>\n", "      <td>FitnessFan</td>\n", "      <td>Instagram</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>Excited about the upcoming weekend getaway!  ...</td>\n", "      <td>0</td>\n", "      <td>1/15/2023 18:20</td>\n", "      <td>AdventureX</td>\n", "      <td>Facebook</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>Trying out a new recipe for dinner tonight.  ...</td>\n", "      <td>2</td>\n", "      <td>1/15/2023 19:55</td>\n", "      <td>ChefCook</td>\n", "      <td>Instagram</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-ec1ad710-797d-4787-9b32-bc3249f6849e')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-ec1ad710-797d-4787-9b32-bc3249f6849e button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-ec1ad710-797d-4787-9b32-bc3249f6849e');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-c8effeac-357c-41b3-a669-764293b7d8bf\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-c8effeac-357c-41b3-a669-764293b7d8bf')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-c8effeac-357c-41b3-a669-764293b7d8bf button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 747,\n  \"fields\": [\n    {\n      \"column\": \"Sno.\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 215,\n        \"min\": 1,\n        \"max\": 747,\n        \"num_unique_values\": 747,\n        \"samples\": [\n          209,\n          260,\n          98\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Text\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 730,\n        \"samples\": [\n          \"Mastered the art of creating paper airplanes during lecture. Paper planes: Soaring to new heights! \",\n          \" Wrestling with thoughts, a perplexed mind lost in the labyrinth of life's complexities. \",\n          \"Imbued with gratitude for the simple pleasure of a warm cup of tea. \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Sentiment\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 2,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          0,\n          1,\n          2\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Timestamp\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"num_unique_values\": 693,\n        \"samples\": [\n          \"9/10/2017 20:15\",\n          \"2/21/2023 19:30\",\n          \"7/2/2016 23:30\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"User\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 677,\n        \"samples\": [\n          \"MiseryMaze  \",\n          \" StormSeeker \",\n          \" MovieBuff2      \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Platform\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 3,\n        \"samples\": [\n          \"Twitter \",\n          \"Instagram \",\n          \"Facebook \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 76}]}, {"cell_type": "code", "source": ["#checking the number of rows and columns\n", "df.shape"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "P9Ed02LLrBYm", "outputId": "7f688832-ad95-4422-80cb-945124ee48e7"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(747, 6)"]}, "metadata": {}, "execution_count": 77}]}, {"cell_type": "code", "source": ["df"], "metadata": {"id": "nJsidOlTrxFz", "colab": {"base_uri": "https://localhost:8080/", "height": 423}, "outputId": "61af4aa8-cfe5-47cf-9b49-be9d4083c04a"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     Sno.                                               Text  Sentiment  \\\n", "0       1   Enjoying a beautiful day at the park!        ...          0   \n", "1       2   Traffic was terrible this morning.           ...          1   \n", "2       3   Just finished an amazing workout! üí™       ...          0   \n", "3       4   Excited about the upcoming weekend getaway!  ...          0   \n", "4       5   Trying out a new recipe for dinner tonight.  ...          2   \n", "..    ...                                                ...        ...   \n", "742   743               Drifting in the void of emptiness.            1   \n", "743   744               Shattered by the echoes of regret.            1   \n", "744   745                 Trapped in the cycle of despair.            1   \n", "745   746             Blinded by the darkness of the soul.            1   \n", "746   747                Suffocated by the weight†of†sorrow.          1   \n", "\n", "           Timestamp                 User    Platform  \n", "0    1/15/2023 12:30        User123          Twitter   \n", "1     1/15/2023 8:45        CommuterX        Twitter   \n", "2    1/15/2023 15:45        <PERSON><PERSON><PERSON>   \n", "3    1/15/2023 18:20        AdventureX      Facebook   \n", "4    1/15/2023 19:55        ChefCook       Instagram   \n", "..               ...                  ...         ...  \n", "742  3/17/2023 19:30       EchoesRegret      Twitter   \n", "743  3/18/2023 19:30       DespairCycle    Instagram   \n", "744  3/19/2023 19:30       SoulDarkness     Facebook   \n", "745  3/20/2023 19:30  SorrowSuffocation      Twitter   \n", "746  3/21/2023 19:30  WeightSuffocation††  Instagram   \n", "\n", "[747 rows x 6 columns]"], "text/html": ["\n", "  <div id=\"df-7761a9f8-3f88-466a-baa1-88345d88562e\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sno.</th>\n", "      <th>Text</th>\n", "      <th>Sentiment</th>\n", "      <th>Timestamp</th>\n", "      <th>User</th>\n", "      <th>Platform</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Enjoying a beautiful day at the park!        ...</td>\n", "      <td>0</td>\n", "      <td>1/15/2023 12:30</td>\n", "      <td>User123</td>\n", "      <td>Twitter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>Traffic was terrible this morning.           ...</td>\n", "      <td>1</td>\n", "      <td>1/15/2023 8:45</td>\n", "      <td>CommuterX</td>\n", "      <td>Twitter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>Just finished an amazing workout! üí™       ...</td>\n", "      <td>0</td>\n", "      <td>1/15/2023 15:45</td>\n", "      <td>FitnessFan</td>\n", "      <td>Instagram</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>Excited about the upcoming weekend getaway!  ...</td>\n", "      <td>0</td>\n", "      <td>1/15/2023 18:20</td>\n", "      <td>AdventureX</td>\n", "      <td>Facebook</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>Trying out a new recipe for dinner tonight.  ...</td>\n", "      <td>2</td>\n", "      <td>1/15/2023 19:55</td>\n", "      <td>ChefCook</td>\n", "      <td>Instagram</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>742</th>\n", "      <td>743</td>\n", "      <td>Drifting in the void of emptiness.</td>\n", "      <td>1</td>\n", "      <td>3/17/2023 19:30</td>\n", "      <td>EchoesRegret</td>\n", "      <td>Twitter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>743</th>\n", "      <td>744</td>\n", "      <td>Shattered by the echoes of regret.</td>\n", "      <td>1</td>\n", "      <td>3/18/2023 19:30</td>\n", "      <td>DespairCycle</td>\n", "      <td>Instagram</td>\n", "    </tr>\n", "    <tr>\n", "      <th>744</th>\n", "      <td>745</td>\n", "      <td>Trapped in the cycle of despair.</td>\n", "      <td>1</td>\n", "      <td>3/19/2023 19:30</td>\n", "      <td>SoulDarkness</td>\n", "      <td>Facebook</td>\n", "    </tr>\n", "    <tr>\n", "      <th>745</th>\n", "      <td>746</td>\n", "      <td>Blinded by the darkness of the soul.</td>\n", "      <td>1</td>\n", "      <td>3/20/2023 19:30</td>\n", "      <td>SorrowSuffocation</td>\n", "      <td>Twitter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>746</th>\n", "      <td>747</td>\n", "      <td>Suffocated by the weight†of†sorrow.</td>\n", "      <td>1</td>\n", "      <td>3/21/2023 19:30</td>\n", "      <td>WeightSuffocation††</td>\n", "      <td>Instagram</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>747 rows × 6 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-7761a9f8-3f88-466a-baa1-88345d88562e')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-7761a9f8-3f88-466a-baa1-88345d88562e button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-7761a9f8-3f88-466a-baa1-88345d88562e');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-41af8b28-849a-4c27-8f5e-4f3b76959ff1\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-41af8b28-849a-4c27-8f5e-4f3b76959ff1')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-41af8b28-849a-4c27-8f5e-4f3b76959ff1 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_c952df63-0d22-4447-ad12-51971035823d\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_c952df63-0d22-4447-ad12-51971035823d button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 747,\n  \"fields\": [\n    {\n      \"column\": \"Sno.\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 215,\n        \"min\": 1,\n        \"max\": 747,\n        \"num_unique_values\": 747,\n        \"samples\": [\n          209,\n          260,\n          98\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Text\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 730,\n        \"samples\": [\n          \"Mastered the art of creating paper airplanes during lecture. Paper planes: Soaring to new heights! \",\n          \" Wrestling with thoughts, a perplexed mind lost in the labyrinth of life's complexities. \",\n          \"Imbued with gratitude for the simple pleasure of a warm cup of tea. \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Sentiment\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 2,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          0,\n          1,\n          2\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Timestamp\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"num_unique_values\": 693,\n        \"samples\": [\n          \"9/10/2017 20:15\",\n          \"2/21/2023 19:30\",\n          \"7/2/2016 23:30\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"User\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 677,\n        \"samples\": [\n          \"MiseryMaze  \",\n          \" StormSeeker \",\n          \" MovieBuff2      \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Platform\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 3,\n        \"samples\": [\n          \"Twitter \",\n          \"Instagram \",\n          \"Facebook \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 78}]}, {"cell_type": "code", "source": ["#counting the number of missing values in the dataset\n", "df.isnull().sum()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EOOgchsRud2I", "outputId": "3dd26098-c3b8-44c5-c436-92a54d6d1b81"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Sno.         0\n", "Text         0\n", "Sentiment    0\n", "Timestamp    0\n", "User         0\n", "Platform     0\n", "dtype: int64"]}, "metadata": {}, "execution_count": 79}]}, {"cell_type": "code", "source": ["df"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "V60cC5Fx-VEl", "outputId": "89402a21-f161-4fca-dd1e-1a96b2b39b81"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     Sno.                                               Text  Sentiment  \\\n", "0       1   Enjoying a beautiful day at the park!        ...          0   \n", "1       2   Traffic was terrible this morning.           ...          1   \n", "2       3   Just finished an amazing workout! üí™       ...          0   \n", "3       4   Excited about the upcoming weekend getaway!  ...          0   \n", "4       5   Trying out a new recipe for dinner tonight.  ...          2   \n", "..    ...                                                ...        ...   \n", "742   743               Drifting in the void of emptiness.            1   \n", "743   744               Shattered by the echoes of regret.            1   \n", "744   745                 Trapped in the cycle of despair.            1   \n", "745   746             Blinded by the darkness of the soul.            1   \n", "746   747                Suffocated by the weight†of†sorrow.          1   \n", "\n", "           Timestamp                 User    Platform  \n", "0    1/15/2023 12:30        User123          Twitter   \n", "1     1/15/2023 8:45        CommuterX        Twitter   \n", "2    1/15/2023 15:45        <PERSON><PERSON><PERSON>   \n", "3    1/15/2023 18:20        AdventureX      Facebook   \n", "4    1/15/2023 19:55        ChefCook       Instagram   \n", "..               ...                  ...         ...  \n", "742  3/17/2023 19:30       EchoesRegret      Twitter   \n", "743  3/18/2023 19:30       DespairCycle    Instagram   \n", "744  3/19/2023 19:30       SoulDarkness     Facebook   \n", "745  3/20/2023 19:30  SorrowSuffocation      Twitter   \n", "746  3/21/2023 19:30  WeightSuffocation††  Instagram   \n", "\n", "[747 rows x 6 columns]"], "text/html": ["\n", "  <div id=\"df-fcc79347-e97e-448d-a7cd-4b54ac25b172\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sno.</th>\n", "      <th>Text</th>\n", "      <th>Sentiment</th>\n", "      <th>Timestamp</th>\n", "      <th>User</th>\n", "      <th>Platform</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Enjoying a beautiful day at the park!        ...</td>\n", "      <td>0</td>\n", "      <td>1/15/2023 12:30</td>\n", "      <td>User123</td>\n", "      <td>Twitter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>Traffic was terrible this morning.           ...</td>\n", "      <td>1</td>\n", "      <td>1/15/2023 8:45</td>\n", "      <td>CommuterX</td>\n", "      <td>Twitter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>Just finished an amazing workout! üí™       ...</td>\n", "      <td>0</td>\n", "      <td>1/15/2023 15:45</td>\n", "      <td>FitnessFan</td>\n", "      <td>Instagram</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>Excited about the upcoming weekend getaway!  ...</td>\n", "      <td>0</td>\n", "      <td>1/15/2023 18:20</td>\n", "      <td>AdventureX</td>\n", "      <td>Facebook</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>Trying out a new recipe for dinner tonight.  ...</td>\n", "      <td>2</td>\n", "      <td>1/15/2023 19:55</td>\n", "      <td>ChefCook</td>\n", "      <td>Instagram</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>742</th>\n", "      <td>743</td>\n", "      <td>Drifting in the void of emptiness.</td>\n", "      <td>1</td>\n", "      <td>3/17/2023 19:30</td>\n", "      <td>EchoesRegret</td>\n", "      <td>Twitter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>743</th>\n", "      <td>744</td>\n", "      <td>Shattered by the echoes of regret.</td>\n", "      <td>1</td>\n", "      <td>3/18/2023 19:30</td>\n", "      <td>DespairCycle</td>\n", "      <td>Instagram</td>\n", "    </tr>\n", "    <tr>\n", "      <th>744</th>\n", "      <td>745</td>\n", "      <td>Trapped in the cycle of despair.</td>\n", "      <td>1</td>\n", "      <td>3/19/2023 19:30</td>\n", "      <td>SoulDarkness</td>\n", "      <td>Facebook</td>\n", "    </tr>\n", "    <tr>\n", "      <th>745</th>\n", "      <td>746</td>\n", "      <td>Blinded by the darkness of the soul.</td>\n", "      <td>1</td>\n", "      <td>3/20/2023 19:30</td>\n", "      <td>SorrowSuffocation</td>\n", "      <td>Twitter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>746</th>\n", "      <td>747</td>\n", "      <td>Suffocated by the weight†of†sorrow.</td>\n", "      <td>1</td>\n", "      <td>3/21/2023 19:30</td>\n", "      <td>WeightSuffocation††</td>\n", "      <td>Instagram</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>747 rows × 6 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-fcc79347-e97e-448d-a7cd-4b54ac25b172')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-fcc79347-e97e-448d-a7cd-4b54ac25b172 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-fcc79347-e97e-448d-a7cd-4b54ac25b172');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-b6b5abd4-7907-432a-bc94-a7c26022de11\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-b6b5abd4-7907-432a-bc94-a7c26022de11')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-b6b5abd4-7907-432a-bc94-a7c26022de11 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_c654bab9-e060-48de-b60b-adb6e65cb04f\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_c654bab9-e060-48de-b60b-adb6e65cb04f button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 747,\n  \"fields\": [\n    {\n      \"column\": \"Sno.\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 215,\n        \"min\": 1,\n        \"max\": 747,\n        \"num_unique_values\": 747,\n        \"samples\": [\n          209,\n          260,\n          98\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Text\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 730,\n        \"samples\": [\n          \"Mastered the art of creating paper airplanes during lecture. Paper planes: Soaring to new heights! \",\n          \" Wrestling with thoughts, a perplexed mind lost in the labyrinth of life's complexities. \",\n          \"Imbued with gratitude for the simple pleasure of a warm cup of tea. \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Sentiment\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 2,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          0,\n          1,\n          2\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Timestamp\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"num_unique_values\": 693,\n        \"samples\": [\n          \"9/10/2017 20:15\",\n          \"2/21/2023 19:30\",\n          \"7/2/2016 23:30\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"User\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 677,\n        \"samples\": [\n          \"MiseryMaze  \",\n          \" StormSeeker \",\n          \" MovieBuff2      \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Platform\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 3,\n        \"samples\": [\n          \"Twitter \",\n          \"Instagram \",\n          \"Facebook \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 80}]}, {"cell_type": "code", "source": ["\n", "#checking the distribution of target column where 0=Positive, 1=Negative, 2=Neutral\n", "df['Sentiment'].value_counts()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2LS9KsK_CpeV", "outputId": "3861e980-713c-4f19-956b-f8a6db334919"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Sentiment\n", "0    315\n", "1    254\n", "2    178\n", "Name: count, dtype: int64"]}, "metadata": {}, "execution_count": 81}]}, {"cell_type": "code", "source": ["# Import necessary modules\n", "import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "# Create a copy of the df DataFrame\n", "df1 = df.copy()\n", "\n", "# Convert Timestamp to datetime\n", "df1['Timestamp'] = pd.to_datetime(df1['Timestamp'])\n", "\n", "# Plot 1: Sentiment Distribution\n", "sns.countplot(x='Sentiment', data=df1)\n", "plt.title('Sentiment Distributin')\n", "plt.xlabel('Sentiment')\n", "plt.ylabel('Count')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 472}, "id": "DKo5ybneVydI", "outputId": "6f464ec2-56b3-40d5-8813-aa3de14615ac"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["0---> Positive\n", "1---> Negative\n", "2---> Neutral"], "metadata": {"id": "6OW0T-sJEFMW"}}, {"cell_type": "markdown", "source": ["Stemming\n", "\n", "Stemming is the process of reducing a words to its root word"], "metadata": {"id": "8YKOkzWIGi7L"}}, {"cell_type": "code", "source": ["port_stem=PorterStemmer()"], "metadata": {"id": "A51g93jJGsJL"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def stemming(content):\n", "  stemmed_content= re.sub('[^a-zA-Z]',' ',content)\n", "  stemmed_content=stemmed_content.lower()\n", "  stemmed_content=stemmed_content.split()\n", "  stemmed_content=[port_stem.stem(word) for word in stemmed_content if not word in stopwords.words('english')]\n", "  stemmed_content=' '.join(stemmed_content)\n", "\n", "  return stemmed_content"], "metadata": {"id": "w6MrNnMhG1fW"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["df['stemmed_content']=df['Text'].apply(stemming)"], "metadata": {"id": "Sjx9-A6yJANp"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["#Showing the stemmed content\n", "df.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "48BLJgtyJPca", "outputId": "a401f65f-5e44-44d8-81ce-d8ca59e8279d"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   Sno.                                               Text  Sentiment  \\\n", "0     1   Enjoying a beautiful day at the park!        ...          0   \n", "1     2   Traffic was terrible this morning.           ...          1   \n", "2     3   Just finished an amazing workout! üí™       ...          0   \n", "3     4   Excited about the upcoming weekend getaway!  ...          0   \n", "4     5   Trying out a new recipe for dinner tonight.  ...          2   \n", "\n", "         Timestamp            User    Platform               stemmed_content  \n", "0  1/15/2023 12:30   User123          Twitter          enjoy beauti day park  \n", "1   1/15/2023 8:45   CommuterX        Twitter           traffic terribl morn  \n", "2  1/15/2023 15:45   <PERSON><PERSON>an     Instagram            finish amaz workout  \n", "3  1/15/2023 18:20   AdventureX      Facebook    excit upcom weekend getaway  \n", "4  1/15/2023 19:55   ChefCook       Instagram   tri new recip dinner tonight  "], "text/html": ["\n", "  <div id=\"df-1c55bccd-821d-4f29-bbd5-415d9fcf164f\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sno.</th>\n", "      <th>Text</th>\n", "      <th>Sentiment</th>\n", "      <th>Timestamp</th>\n", "      <th>User</th>\n", "      <th>Platform</th>\n", "      <th>stemmed_content</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Enjoying a beautiful day at the park!        ...</td>\n", "      <td>0</td>\n", "      <td>1/15/2023 12:30</td>\n", "      <td>User123</td>\n", "      <td>Twitter</td>\n", "      <td>enjoy beauti day park</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>Traffic was terrible this morning.           ...</td>\n", "      <td>1</td>\n", "      <td>1/15/2023 8:45</td>\n", "      <td>CommuterX</td>\n", "      <td>Twitter</td>\n", "      <td>traffic terribl morn</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>Just finished an amazing workout! üí™       ...</td>\n", "      <td>0</td>\n", "      <td>1/15/2023 15:45</td>\n", "      <td>FitnessFan</td>\n", "      <td>Instagram</td>\n", "      <td>finish amaz workout</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>Excited about the upcoming weekend getaway!  ...</td>\n", "      <td>0</td>\n", "      <td>1/15/2023 18:20</td>\n", "      <td>AdventureX</td>\n", "      <td>Facebook</td>\n", "      <td>excit upcom weekend getaway</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>Trying out a new recipe for dinner tonight.  ...</td>\n", "      <td>2</td>\n", "      <td>1/15/2023 19:55</td>\n", "      <td>ChefCook</td>\n", "      <td>Instagram</td>\n", "      <td>tri new recip dinner tonight</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-1c55bccd-821d-4f29-bbd5-415d9fcf164f')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-1c55bccd-821d-4f29-bbd5-415d9fcf164f button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-1c55bccd-821d-4f29-bbd5-415d9fcf164f');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-f29ac096-8c18-48b0-ad6e-75a7383afec0\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-f29ac096-8c18-48b0-ad6e-75a7383afec0')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-f29ac096-8c18-48b0-ad6e-75a7383afec0 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 747,\n  \"fields\": [\n    {\n      \"column\": \"Sno.\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 215,\n        \"min\": 1,\n        \"max\": 747,\n        \"num_unique_values\": 747,\n        \"samples\": [\n          209,\n          260,\n          98\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Text\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 730,\n        \"samples\": [\n          \"Mastered the art of creating paper airplanes during lecture. Paper planes: Soaring to new heights! \",\n          \" Wrestling with thoughts, a perplexed mind lost in the labyrinth of life's complexities. \",\n          \"Imbued with gratitude for the simple pleasure of a warm cup of tea. \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Sentiment\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 2,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          0,\n          1,\n          2\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Timestamp\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"num_unique_values\": 693,\n        \"samples\": [\n          \"9/10/2017 20:15\",\n          \"2/21/2023 19:30\",\n          \"7/2/2016 23:30\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"User\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 677,\n        \"samples\": [\n          \"MiseryMaze  \",\n          \" StormSeeker \",\n          \" MovieBuff2      \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Platform\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 3,\n        \"samples\": [\n          \"Twitter \",\n          \"Instagram \",\n          \"Facebook \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"stemmed_content\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 722,\n        \"samples\": [\n          \"sink like autumn leav river sorrow carri away current\",\n          \"explor new hobbi photographi free time captur moment len\",\n          \"calm amidst chao\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 86}]}, {"cell_type": "code", "source": ["print(df['stemmed_content'])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2laoaNYrJlGH", "outputId": "bcd5a870-a0a0-487e-fa68-c5e5b2abe4b6"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["0             enjoy beauti day park\n", "1              traffic terribl morn\n", "2               finish amaz workout\n", "3       excit upcom weekend getaway\n", "4      tri new recip dinner tonight\n", "                   ...             \n", "742                drift void empti\n", "743             shatter echo regret\n", "744               trap cycl despair\n", "745                 blind dark soul\n", "746            suffoc weight sorrow\n", "Name: stemmed_content, Length: 747, dtype: object\n"]}]}, {"cell_type": "code", "source": ["print(df['Sentiment'])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cT5thMzTJ2dR", "outputId": "a3eb0758-8db7-4a86-d73f-ca31740e9d8c"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["0      0\n", "1      1\n", "2      0\n", "3      0\n", "4      2\n", "      ..\n", "742    1\n", "743    1\n", "744    1\n", "745    1\n", "746    1\n", "Name: Sentiment, Length: 747, dtype: int64\n"]}]}, {"cell_type": "code", "source": ["#separating the data and label\n", "X=df['stemmed_content'].values\n", "Y=df['Sentiment'].values"], "metadata": {"id": "RmdKQVQGJ-n6"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Splitting the data to training data and test data"], "metadata": {"id": "lojYhn8NKtfl"}}, {"cell_type": "code", "source": ["X_train,X_test,Y_train,Y_test = train_test_split(X,Y, test_size=0.2, stratify=Y,random_state=2)"], "metadata": {"id": "IkFhMQaJKxwh"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(X.shape, X_train.shape, X_test.shape)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TO9TKzDKLSt1", "outputId": "a2f02c32-2f58-4581-d20f-e034a8ea5aa5"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["(747,) (597,) (150,)\n"]}]}, {"cell_type": "code", "source": ["#Convertinng the textual data to numerical data\n", "\n", "vectorizer= TfidfVectorizer()\n", "\n", "X_train= vectorizer.fit_transform(X_train)\n", "X_test= vectorizer.transform(X_test)"], "metadata": {"id": "PTxFpt6aLafj"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(X_train)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AbwXS6hNMqoW", "outputId": "dd9c9a82-82cb-44e2-f45d-e9f36a0fd1b5"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["  (0, 390)\t0.2210941051802207\n", "  (0, 1450)\t0.35822286256940916\n", "  (0, 1287)\t0.2841080179378772\n", "  (0, 1473)\t0.2541895256324762\n", "  (0, 1141)\t0.3211654402536432\n", "  (0, 850)\t0.6184712297721845\n", "  (0, 886)\t0.24705059562211124\n", "  (0, 1285)\t0.2670826664208189\n", "  (0, 791)\t0.24380944817696062\n", "  (1, 11)\t0.28685291619174996\n", "  (1, 411)\t0.3005899512941571\n", "  (1, 1077)\t0.28685291619174996\n", "  (1, 1152)\t0.2481303890949894\n", "  (1, 1299)\t0.3005899512941571\n", "  (1, 1009)\t0.3005899512941571\n", "  (1, 1006)\t0.24309934531230162\n", "  (1, 621)\t0.6011799025883142\n", "  (1, 241)\t0.28685291619174996\n", "  (2, 263)\t0.267922502066469\n", "  (2, 680)\t0.29822854066909976\n", "  (2, 579)\t0.24896276370037737\n", "  (2, 731)\t0.2888281162154258\n", "  (2, 1127)\t0.29822854066909976\n", "  (2, 898)\t0.3097337303643826\n", "  (2, 585)\t0.32456649965570417\n", "  :\t:\n", "  (592, 514)\t0.22841327789945443\n", "  (592, 190)\t0.2123105340995992\n", "  (592, 368)\t0.2667742283581516\n", "  (592, 1297)\t0.272821045957789\n", "  (593, 972)\t0.4876151868684369\n", "  (593, 734)\t0.4876151868684369\n", "  (593, 47)\t0.45810804420045614\n", "  (593, 39)\t0.4371723933020056\n", "  (593, 969)\t0.35139746362765373\n", "  (594, 1153)\t0.8306386199566201\n", "  (594, 672)\t0.4101138212957381\n", "  (594, 390)\t0.3766246627861338\n", "  (595, 274)\t0.6655753386331443\n", "  (595, 1169)\t0.7463306697459077\n", "  (596, 609)\t0.3278074448974214\n", "  (596, 1383)\t0.34045374235979436\n", "  (596, 68)\t0.34045374235979436\n", "  (596, 1131)\t0.30873842870994433\n", "  (596, 1251)\t0.3278074448974214\n", "  (596, 259)\t0.30873842870994433\n", "  (596, 754)\t0.301170754440403\n", "  (596, 190)\t0.23437240153501313\n", "  (596, 769)\t0.21437682456994228\n", "  (596, 93)\t0.3278074448974214\n", "  (596, 322)\t0.2521484330055224\n"]}]}, {"cell_type": "code", "source": ["print(X_test)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xcVZuwwOMxPZ", "outputId": "e9c41d3f-587e-4f68-fabe-b6d9a1f3acf8"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["  (0, 1176)\t0.3304386597946522\n", "  (0, 721)\t0.23812897970237118\n", "  (0, 708)\t0.24636740858319003\n", "  (0, 561)\t0.3304386597946522\n", "  (0, 514)\t0.21941409304917434\n", "  (0, 462)\t0.2620722530408481\n", "  (0, 433)\t0.3104427676646397\n", "  (0, 416)\t0.2762595642877376\n", "  (0, 293)\t0.28525092287747683\n", "  (0, 238)\t0.29625545641775014\n", "  (0, 148)\t0.3304386597946522\n", "  (0, 59)\t0.3104427676646397\n", "  (1, 1216)\t0.35239122107538606\n", "  (1, 949)\t0.3741564218866025\n", "  (1, 868)\t0.40719992762255447\n", "  (1, 849)\t0.433428033657498\n", "  (1, 678)\t0.3885907902399989\n", "  (1, 462)\t0.34375354682249976\n", "  (1, 292)\t0.3361345781701119\n", "  (2, 1192)\t0.717077230057065\n", "  (2, 1180)\t0.30267422027424296\n", "  (2, 998)\t0.3816324030542189\n", "  (2, 775)\t0.2564866442228701\n", "  (2, 223)\t0.3190595236385445\n", "  (2, 62)\t0.28453627741461096\n", "  :\t:\n", "  (147, 1021)\t0.42588589147018285\n", "  (147, 530)\t0.4750264008788941\n", "  (147, 523)\t0.41006618089444197\n", "  (147, 513)\t0.4462810456084629\n", "  (147, 458)\t0.4750264008788941\n", "  (148, 1397)\t0.3700417439158951\n", "  (148, 1335)\t0.4127387201781108\n", "  (148, 1203)\t0.4127387201781108\n", "  (148, 1147)\t0.3877625901705333\n", "  (148, 954)\t0.32008948390074005\n", "  (148, 769)\t0.23300778974894765\n", "  (148, 330)\t0.3877625901705333\n", "  (148, 190)\t0.2547411333728805\n", "  (149, 1480)\t0.31457116179015504\n", "  (149, 1360)\t0.28904430561003047\n", "  (149, 1219)\t0.26555739639932757\n", "  (149, 1196)\t0.27993337627860027\n", "  (149, 1182)\t0.24529559076704546\n", "  (149, 984)\t0.3001951819108824\n", "  (149, 758)\t0.24964370253574242\n", "  (149, 698)\t0.31457116179015504\n", "  (149, 556)\t0.3001951819108824\n", "  (149, 390)\t0.20665793016702774\n", "  (149, 141)\t0.3348329674224372\n", "  (149, 84)\t0.3348329674224372\n"]}]}, {"cell_type": "markdown", "source": ["Training the Machine Learning Model"], "metadata": {"id": "qOttwAmrNADc"}}, {"cell_type": "markdown", "source": ["Logistic Regression"], "metadata": {"id": "LxclJ0w_NG1n"}}, {"cell_type": "code", "source": ["model=LogisticRegression(max_iter=1000)"], "metadata": {"id": "hCaXXyJ3NE0m"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["model.fit(X_train,Y_train)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 74}, "id": "NAgZaXXfNQsb", "outputId": "245802f0-86a3-4770-c410-de38a3a2d8fa"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["LogisticRegression(max_iter=1000)"], "text/html": ["<style>#sk-container-id-2 {color: black;background-color: white;}#sk-container-id-2 pre{padding: 0;}#sk-container-id-2 div.sk-toggleable {background-color: white;}#sk-container-id-2 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-2 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-2 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-2 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-2 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-2 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-2 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-2 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-2 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-2 div.sk-item {position: relative;z-index: 1;}#sk-container-id-2 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-2 div.sk-item::before, #sk-container-id-2 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-2 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-2 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-2 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-2 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-2 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-2 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-2 div.sk-label-container {text-align: center;}#sk-container-id-2 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-2 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LogisticRegression(max_iter=1000)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" checked><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">LogisticRegression</label><div class=\"sk-toggleable__content\"><pre>LogisticRegression(max_iter=1000)</pre></div></div></div></div></div>"]}, "metadata": {}, "execution_count": 96}]}, {"cell_type": "markdown", "source": ["Model Evaluation"], "metadata": {"id": "VltlH4itNke8"}}, {"cell_type": "markdown", "source": ["Accuracy Score"], "metadata": {"id": "dlY9kqFTNnRL"}}, {"cell_type": "code", "source": ["#accuracy score on the training data\n", "X_train_prediction= model.predict(X_train)\n", "training_data_accuracy=accuracy_score(Y_train,X_train_prediction)"], "metadata": {"id": "JoSQjD6fNVU9"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print('Accuracy score on the training data:', training_data_accuracy)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7e_NHDbsON7p", "outputId": "7053fab3-08a8-424e-91e4-581ec6f89cb8"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Accuracy score on the training data: 0.9631490787269682\n"]}]}, {"cell_type": "code", "source": ["#accuracy score on the test data\n", "X_test_prediction= model.predict(X_test)\n", "test_data_accuracy=accuracy_score(Y_test,X_test_prediction)"], "metadata": {"id": "FipjbUnpOWje"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print('Accuracy score on the test data:', test_data_accuracy)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Xvk7y0BXOt0C", "outputId": "a183e966-6440-4d50-c0e2-fddd87acfe1b"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Accuracy score on the test data: 0.8266666666666667\n"]}]}, {"cell_type": "markdown", "source": ["Confusion Matrix"], "metadata": {"id": "uXqaIQLFYGj-"}}, {"cell_type": "code", "source": ["from sklearn.metrics import confusion_matrix\n", "\n", "# Generate confusion matrix\n", "conf_matrix = confusion_matrix(Y_test, X_test_prediction)\n", "\n", "# Plot confusion matrix\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues')\n", "plt.title('Confusion Matrix')\n", "plt.xlabel('Predicted Label')\n", "plt.ylabel('True Label')\n", "plt.show()\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 564}, "id": "5AIiBK2NYFSt", "outputId": "08904b0f-dca6-4b77-9af6-6c73e4c791f6"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x600 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["Classification Report: Print a classification report which includes precision, recall, F1-score, and support for each class (positive, negative, neutral)."], "metadata": {"id": "Rt1WjWoTYMr_"}}, {"cell_type": "code", "source": ["from sklearn.metrics import classification_report\n", "\n", "# Generate classification report\n", "class_report = classification_report(Y_test, X_test_prediction)\n", "\n", "# Print classification report\n", "print(\"Classification Report:\\n\", class_report)\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iIoFj7c8YQet", "outputId": "3c21c585-e987-43ee-8bf6-f304934e5e94"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.78      0.94      0.85        63\n", "           1       0.92      0.86      0.89        51\n", "           2       0.81      0.58      0.68        36\n", "\n", "    accuracy                           0.83       150\n", "   macro avg       0.83      0.79      0.81       150\n", "weighted avg       0.83      0.83      0.82       150\n", "\n"]}]}, {"cell_type": "markdown", "source": ["ROC Curve"], "metadata": {"id": "0jHJzn8gYZEg"}}, {"cell_type": "code", "source": ["# Import necessary modules\n", "from sklearn.metrics import roc_curve, auc\n", "from sklearn.preprocessing import label_binarize\n", "import matplotlib.pyplot as plt\n", "\n", "# Binarize the target variable\n", "Y_test_binarized = label_binarize(Y_test, classes=['0', '1'])\n", "\n", "# Convert X_test_prediction to float\n", "X_test_prediction = X_test_prediction.astype(float)\n", "\n", "# Compute ROC curve and AUC score\n", "fpr, tpr, thresholds = roc_curve(Y_test_binarized[:, 1], X_test_prediction)\n", "roc_auc = auc(fpr, tpr)\n", "\n", "# Plot ROC curve\n", "plt.figure()\n", "plt.plot(fpr, tpr, color='darkorange', lw=2, label='ROC curve (area = %0.2f)' % roc_auc)\n", "plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')\n", "plt.xlim([0.0, 1.0])\n", "plt.ylim([0.0, 1.05])\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title('Receiver Operating Characteristic')\n", "plt.legend(loc='lower right')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 507}, "id": "ffr1otwOYcA8", "outputId": "400fedaf-26a3-4c4f-c630-41c3ed27e083"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_ranking.py:1029: UndefinedMetricWarning: No positive samples in y_true, true positive value should be meaningless\n", "  warnings.warn(\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["sample_predictions = pd.DataFrame({ 'True_Sentiment': Y_test, 'Predicted_Sentiment': X_test_prediction})\n", "print(\"\\nSample Predictions:\")\n", "print(sample_predictions.head())"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "g-72i97Bbt0x", "outputId": "c2961d7f-92df-4dd2-cb69-b43d0810954b"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Sample Predictions:\n", "   True_Sentiment  Predicted_Sentiment\n", "0               0                  0.0\n", "1               0                  0.0\n", "2               2                  0.0\n", "3               2                  2.0\n", "4               2                  2.0\n"]}]}, {"cell_type": "markdown", "source": ["Correct vs. Incorrect Predictions"], "metadata": {"id": "GwrPce4RZDRd"}}, {"cell_type": "code", "source": ["import pickle"], "metadata": {"id": "HFoJgLhiOwra"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["filename= 'trained_model.sav'\n", "pickle.dump(model,open(filename, 'wb'))"], "metadata": {"id": "Nn3n-_v7PPat"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Using the saved model for future Prediction"], "metadata": {"id": "I6JwvwBUPeZP"}}, {"cell_type": "code", "source": ["#Loading the saved model\n", "loaded_model= pickle.load(open('/content/trained_model.sav', 'rb'))"], "metadata": {"id": "sCk2-cwaPcd0"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["X_new=X_test[10]\n", "print(Y_test[10])\n", "\n", "prediction=model.predict(X_new)\n", "print(prediction)\n", "\n", "if prediction[0] == '0':\n", "  print(\"Positive\")\n", "elif prediction[0] == '1':\n", "  print(\"Negative\")\n", "else:\n", "\n", "  print(\"Neutral\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kVjiQxujPzYT", "outputId": "9369f090-1192-466e-b3b5-81b349f5f45b"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["2\n", "[2]\n", "Neutral\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "Jj7rZQA5QLZr"}, "execution_count": null, "outputs": []}]}